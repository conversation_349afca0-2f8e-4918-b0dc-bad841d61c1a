// YouTube API service for fetching video data

export interface YouTubeVideo {
  id: string;
  title: string;
  description: string;
  publishedAt: string;
  thumbnailUrl: string;
  channelId: string;
  channelTitle: string;
  viewCount: number;
  likeCount: number;
  commentCount: number;
  subscriberCount?: number;
  engagementRate?: number;
}

export interface SearchFilters {
  region?: string;
  language?: string;
  timeRange?: 'day' | 'week' | 'month' | 'year' | 'all';
  maxResults?: number;
}

const DEFAULT_FILTERS: SearchFilters = {
  region: 'US',
  language: 'en',
  timeRange: 'month',
  maxResults: 10
};

// Helper function to format date based on timeRange
const getPublishedAfter = (timeRange: SearchFilters['timeRange']) => {
  const now = new Date();
  switch (timeRange) {
    case 'day':
      now.setDate(now.getDate() - 1);
      break;
    case 'week':
      now.setDate(now.getDate() - 7);
      break;
    case 'month':
      now.setMonth(now.getMonth() - 1);
      break;
    case 'year':
      now.setFullYear(now.getFullYear() - 1);
      break;
    default:
      now.setFullYear(now.getFullYear() - 5); // Default to 5 years
  }
  return now.toISOString();
};

// Search for videos based on a query
export async function searchYouTubeVideos(
  query: string,
  filters: SearchFilters = DEFAULT_FILTERS
): Promise<YouTubeVideo[]> {
  try {
    const apiKey = process.env.NEXT_PUBLIC_YOUTUBE_API_KEY;
    if (!apiKey) {
      throw new Error('YouTube API key is not configured');
    }

    const { region, language, timeRange, maxResults } = { ...DEFAULT_FILTERS, ...filters };
    const publishedAfter = getPublishedAfter(timeRange);
    
    // Step 1: Search for videos matching the query
    const searchUrl = new URL('https://www.googleapis.com/youtube/v3/search');
    searchUrl.searchParams.append('key', apiKey);
    searchUrl.searchParams.append('q', query);
    searchUrl.searchParams.append('part', 'snippet');
    searchUrl.searchParams.append('type', 'video');
    searchUrl.searchParams.append('maxResults', maxResults?.toString() || '10');
    searchUrl.searchParams.append('publishedAfter', publishedAfter);
    searchUrl.searchParams.append('relevanceLanguage', language || 'en');
    searchUrl.searchParams.append('regionCode', region || 'US');
    searchUrl.searchParams.append('order', 'viewCount'); // Sort by view count

    const searchResponse = await fetch(searchUrl.toString());
    if (!searchResponse.ok) {
      throw new Error(`YouTube search API error: ${searchResponse.statusText}`);
    }
    
    const searchData = await searchResponse.json();
    const videoIds = searchData.items.map((item: any) => item.id.videoId).join(',');
    
    if (!videoIds) {
      return [];
    }
    
    // Step 2: Get detailed video statistics
    const videosUrl = new URL('https://www.googleapis.com/youtube/v3/videos');
    videosUrl.searchParams.append('key', apiKey);
    videosUrl.searchParams.append('id', videoIds);
    videosUrl.searchParams.append('part', 'snippet,statistics');
    
    const videosResponse = await fetch(videosUrl.toString());
    if (!videosResponse.ok) {
      throw new Error(`YouTube videos API error: ${videosResponse.statusText}`);
    }
    
    const videosData = await videosResponse.json();
    
    // Step 3: Get channel details for subscriber counts
    const channelIds = videosData.items.map((item: any) => item.snippet.channelId).join(',');
    const channelsUrl = new URL('https://www.googleapis.com/youtube/v3/channels');
    channelsUrl.searchParams.append('key', apiKey);
    channelsUrl.searchParams.append('id', channelIds);
    channelsUrl.searchParams.append('part', 'statistics');
    
    const channelsResponse = await fetch(channelsUrl.toString());
    if (!channelsResponse.ok) {
      throw new Error(`YouTube channels API error: ${channelsResponse.statusText}`);
    }
    
    const channelsData = await channelsResponse.json();
    
    // Create a map of channel IDs to subscriber counts
    const subscriberCountMap = new Map();
    channelsData.items.forEach((channel: any) => {
      subscriberCountMap.set(
        channel.id, 
        parseInt(channel.statistics.subscriberCount, 10)
      );
    });
    
    // Process and combine the data
    const videos: YouTubeVideo[] = videosData.items.map((item: any) => {
      const viewCount = parseInt(item.statistics.viewCount, 10) || 0;
      const likeCount = parseInt(item.statistics.likeCount, 10) || 0;
      const commentCount = parseInt(item.statistics.commentCount, 10) || 0;
      const subscriberCount = subscriberCountMap.get(item.snippet.channelId) || 0;
      
      // Calculate engagement rate: (likes + comments) / views * 100
      const engagementRate = viewCount > 0 
        ? ((likeCount + commentCount) / viewCount) * 100 
        : 0;
      
      return {
        id: item.id,
        title: item.snippet.title,
        description: item.snippet.description,
        publishedAt: item.snippet.publishedAt,
        thumbnailUrl: item.snippet.thumbnails.medium.url,
        channelId: item.snippet.channelId,
        channelTitle: item.snippet.channelTitle,
        viewCount,
        likeCount,
        commentCount,
        subscriberCount,
        engagementRate
      };
    });
    
    // Sort by engagement rate (descending)
    return videos.sort((a, b) => b.engagementRate! - a.engagementRate!);
    
  } catch (error) {
    console.error('Error searching YouTube videos:', error);
    throw error;
  }
}

// Format numbers for display (e.g., 1000 -> 1K)
export function formatNumber(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
}

// Format date for display
export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}
