import HeaderAuth from "@/components/header-auth";
import { ThemeSwitcher } from "@/components/theme-switcher";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import { ThemeProvider } from "next-themes";
import Link from "next/link";
import "./globals.css";
import { TrendingUp } from "lucide-react";

const defaultUrl = process.env.VERCEL_URL
  ? `https://${process.env.VERCEL_URL}`
  : "http://localhost:3000";

export const metadata = {
  metadataBase: new URL(defaultUrl),
  title: "YouTuber Insights",
  description:
    "Find the top-performing videos in your niche and level-up your content strategy.",
};

const geistSans = Geist({
  display: "swap",
  subsets: ["latin"],
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={geistSans.className} suppressHydrationWarning>
      <body className="bg-background text-foreground">
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <main className="min-h-screen flex flex-col items-center">
            <div className="flex-1 w-full flex flex-col gap-20 items-center">
              <nav className="w-full flex justify-center border-b border-b-foreground/10 h-16">
                <div className="w-full max-w-5xl flex justify-between items-center p-3 px-5 text-sm">
                  <div className="flex gap-5 items-center font-semibold">
                    <Link href={"/"}>YouTuber Insights</Link>
                  </div>
                  <div className="flex items-center gap-5">
                    <HeaderAuth />
                    <ThemeSwitcher />
                  </div>
                </div>
              </nav>
              <div className="flex flex-col gap-20 max-w-5xl p-5">
                {children}
              </div>

              {/* Footer */}
              <footer className="w-full flex items-center justify-center border-t mx-auto text-center text-xs gap-8 py-8">
                <div className="container mx-auto px-8">
                  <div className="flex flex-col md:flex-row justify-around">
                    <div className="flex flex-col items-center mb-8 md:mb-0">
                      <div className="flex items-center space-x-2 mb-4">
                        <TrendingUp className="h-6 w-6 text-red-600" />
                        <span className="font-bold text-xl">
                          YouTuber Insights
                        </span>
                      </div>
                      <p className="text-muted-foreground max-w-xs">
                        Helping content creators make data-driven decisions to
                        grow their YouTube channels.
                      </p>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-3 gap-8">
                      <div>
                        <h3 className="font-semibold mb-4">Product</h3>
                        <ul className="space-y-2">
                          <li>
                            <a
                              href="/about"
                              className="text-muted-foreground hover:text-foreground"
                            >
                              About
                            </a>
                          </li>
                          <li>
                            <a
                              href="/pricing"
                              className="text-muted-foreground hover:text-foreground"
                            >
                              Pricing
                            </a>
                          </li>
                        </ul>
                      </div>

                      <div>
                        <h3 className="font-semibold mb-4">Company</h3>
                        <ul className="space-y-2">
                          <li>
                            <a
                              href="/privacy"
                              className="text-muted-foreground hover:text-foreground"
                            >
                              Privacy
                            </a>
                          </li>
                          <li>
                            <a
                              href="/terms"
                              className="text-muted-foreground hover:text-foreground"
                            >
                              Terms
                            </a>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                  <div className="mt-8 flex items-center justify-center">
                    <p className="text-xs text-muted-foreground">
                      © {new Date().getFullYear()} YouTuber Insights. All
                      rights reserved.
                    </p>
                  </div>
                </div>
              </footer>
            </div>
          </main>
        </ThemeProvider>
      </body>
    </html>
  );
}
