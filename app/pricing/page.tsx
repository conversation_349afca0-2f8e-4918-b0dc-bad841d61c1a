"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Check, Crown, Rocket, Zap, Star, Loader2, X } from "lucide-react";
import { createClient } from "@/utils/supabase/client";
import { useSearchParams } from "next/navigation";

const plans = [
  {
    name: "Free",
    price: "$0",
    period: "forever",
    description: "Perfect for getting started",
    icon: Zap,
    features: [
      "5 searches per day",
      "Basic video insights",
      "CSV & TXT export",
      "Standard filters",
      "Community support",
    ],
    limitations: ["Limited search history", "Basic metrics only"],
    cta: "Get Started",
    ctaVariant: "outline" as const,
    popular: false,
  },
  {
    name: "Pro",
    price: "$10",
    period: "per month",
    description: "For serious content creators",
    icon: Crown,
    features: [
      "100 searches per day",
      "Advanced video insights",
      "CSV & TXT export",
      "All filters & regions",
      "Search history (30 days)",
      "Engagement analytics",
      "Priority support",
    ],
    limitations: [],
    cta: "Start Pro Trial",
    ctaVariant: "default" as const,
    popular: true,
  },
  {
    name: "Business",
    price: "$49",
    period: "per month",
    description: "For teams and agencies",
    icon: Rocket,
    features: [
      "Unlimited searches",
      "Premium video insights",
      "CSV & TXT export",
      "All filters & regions",
      "Unlimited search history",
      "Advanced analytics",
      "API access",
      "Team collaboration",
      "Priority support",
      "Custom integrations",
    ],
    limitations: [],
    cta: "Start Business Trial",
    ctaVariant: "default" as const,
    popular: false,
  },
];

export default function Pricing() {
  const [loadingPlan, setLoadingPlan] = useState<string | null>(null);
  const [currentTier, setCurrentTier] = useState<string | null>(null);
  const [isLoggedIn, setIsLoggedIn] = useState<boolean>(false);
  const [message, setMessage] = useState<string | null>(null);
  const searchParams = useSearchParams();

  useEffect(() => {
    // Check for message in URL params
    const urlMessage = searchParams.get("message");
    if (urlMessage) {
      setMessage(urlMessage);
    }

    const fetchUserAndTier = async () => {
      const supabase = createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (user) {
        setIsLoggedIn(true);
        // Fetch user's current tier
        try {
          const response = await fetch("/api/rate-limit/info");
          if (response.ok) {
            const data = await response.json();
            setCurrentTier(data.tier);
          }
        } catch (error) {
          console.error("Error fetching user tier:", error);
        }
      } else {
        setIsLoggedIn(false);
      }
    };

    fetchUserAndTier();
  }, [searchParams]);

  const handleSubscribe = async (planName: string) => {
    if (planName === "Free") return;

    setLoadingPlan(planName);

    try {
      const response = await fetch("/api/stripe/checkout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          plan: planName.toLowerCase(),
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to create checkout session");
      }

      const { url } = await response.json();

      if (url) {
        window.location.href = url;
      }
    } catch (error) {
      console.error("Error creating checkout session:", error);
      alert("Failed to start checkout. Please try again.");
    } finally {
      setLoadingPlan(null);
    }
  };

  const handleDowngrade = async (planName: string) => {
    setLoadingPlan(planName);

    try {
      // Show confirmation message for downgrades
      const confirmMessage =
        planName === "Free"
          ? "You'll be redirected to manage your subscription. You can cancel your subscription to downgrade to the Free plan."
          : "You'll be redirected to manage your subscription. You can change your plan to downgrade to Pro.";

      if (!confirm(confirmMessage)) {
        setLoadingPlan(null);
        return;
      }

      const response = await fetch("/api/stripe/portal", {
        method: "POST",
      });

      if (response.ok) {
        const { url } = await response.json();
        window.location.href = url;
      } else {
        alert("No active subscription found.");
      }
    } catch (error) {
      console.error("Error opening customer portal:", error);
      alert("Failed to open subscription management. Please try again.");
    } finally {
      setLoadingPlan(null);
    }
  };

  const handlePlanAction = (planName: string) => {
    if (!currentTier || currentTier === "free") {
      // User is on free plan or not logged in - handle upgrades
      if (planName === "Free") return; // Do nothing for free plan
      handleSubscribe(planName);
    } else {
      // User has a paid plan - determine if it's upgrade or downgrade
      const planHierarchy = { free: 0, pro: 1, business: 2 };
      const currentLevel =
        planHierarchy[currentTier as keyof typeof planHierarchy];
      const targetLevel =
        planHierarchy[planName.toLowerCase() as keyof typeof planHierarchy];

      if (targetLevel > currentLevel) {
        // Upgrade - use checkout
        handleSubscribe(planName);
      } else if (targetLevel < currentLevel) {
        // Downgrade - use customer portal
        handleDowngrade(planName);
      }
    }
  };

  return (
    <div className={`container mx-auto max-w-6xl ${message && "-mt-12"}`}>
      {message && (
        <div className="mb-8 bg-orange-50 dark:bg-orange-950/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4">
          <div className="flex items-center justify-center">
            <div className="flex items-center gap-3">
              <p className="text-orange-800 dark:text-orange-200 font-medium">
                {message}
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setMessage(null)}
              className="ml-4 text-orange-600 hover:text-orange-800 dark:text-orange-400 dark:hover:text-orange-200"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">Simple, Transparent Pricing</h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Choose the plan that fits your content creation needs. Upgrade or
          downgrade at any time.
        </p>
      </div>

      <div className="grid md:grid-cols-3 gap-8 mb-12 items-stretch">
        {plans.map((plan) => {
          const Icon = plan.icon;
          const isCurrentPlan = currentTier === plan.name.toLowerCase();
          const getButtonText = () => {
            if (isCurrentPlan) return "Current Plan";
            if (!currentTier || currentTier === "free") {
              return plan.name === "Free"
                ? "Get Started"
                : `Upgrade to ${plan.name}`;
            }
            if (currentTier === "pro") {
              if (plan.name === "Free") return "Downgrade to Free";
              if (plan.name === "Business") return "Upgrade to Business";
            }
            if (currentTier === "business") {
              if (plan.name === "Free") return "Downgrade to Free";
              if (plan.name === "Pro") return "Downgrade to Pro";
            }
            return plan.cta;
          };

          return (
            <Card
              key={plan.name}
              className={`relative flex flex-col h-full ${
                plan.popular && !isCurrentPlan
                  ? "border-primary shadow-lg scale-105"
                  : isCurrentPlan
                    ? "border-green-500 shadow-lg"
                    : ""
              }`}
            >
              {plan.popular && !isCurrentPlan && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-primary text-primary-foreground px-3 py-1 hover:bg-primary">
                    <Star className="h-3 w-3 mr-1" />
                    Most Popular
                  </Badge>
                </div>
              )}

              {isCurrentPlan && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-green-500 text-white px-3 py-1 hover:bg-green-500">
                    <Check className="h-3 w-3 mr-1" />
                    Current Plan
                  </Badge>
                </div>
              )}

              <CardHeader className="text-center pb-4">
                <div className="flex justify-center mb-4">
                  <div
                    className={`p-3 rounded-full ${
                      plan.name === "Free"
                        ? "bg-gray-100 dark:bg-gray-800"
                        : plan.name === "Pro"
                          ? "bg-blue-100 dark:bg-blue-900/20"
                          : "bg-purple-100 dark:bg-purple-900/20"
                    }`}
                  >
                    <Icon
                      className={`h-6 w-6 ${
                        plan.name === "Free"
                          ? "text-gray-600"
                          : plan.name === "Pro"
                            ? "text-blue-600"
                            : "text-purple-600"
                      }`}
                    />
                  </div>
                </div>

                <CardTitle className="text-2xl">{plan.name}</CardTitle>
                <div className="mt-4">
                  <span className="text-4xl font-bold">{plan.price}</span>
                  <span className="text-muted-foreground">/{plan.period}</span>
                </div>
                <p className="text-sm text-muted-foreground mt-2">
                  {plan.description}
                </p>
              </CardHeader>

              <CardContent className="pt-0 flex flex-col flex-grow">
                <div className="space-y-4 flex flex-col flex-grow">
                  <ul className="space-y-3 flex-grow">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start gap-3">
                        <Check className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  {plan.limitations.length > 0 && (
                    <div className="pt-4 border-t">
                      <p className="text-xs text-muted-foreground mb-2">
                        Limitations:
                      </p>
                      <ul className="space-y-1">
                        {plan.limitations.map((limitation, index) => (
                          <li
                            key={index}
                            className="text-xs text-muted-foreground"
                          >
                            • {limitation}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  <div className="pt-6 mt-auto">
                    {plan.name === "Free" &&
                    !isCurrentPlan &&
                    (!currentTier || currentTier === "free") ? (
                      <Link href="/sign-up">
                        <Button variant={plan.ctaVariant} className="w-full">
                          {getButtonText()}
                        </Button>
                      </Link>
                    ) : (
                      <Button
                        variant={isCurrentPlan ? "secondary" : plan.ctaVariant}
                        className="w-full"
                        onClick={() =>
                          !isCurrentPlan && handlePlanAction(plan.name)
                        }
                        disabled={isCurrentPlan || loadingPlan === plan.name}
                      >
                        {loadingPlan === plan.name ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Processing...
                          </>
                        ) : (
                          getButtonText()
                        )}
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* FAQ Section */}
      <div className="max-w-3xl mx-auto">
        <h2 className="text-2xl font-bold text-center mb-8">
          Frequently Asked Questions
        </h2>
        <div className="space-y-6">
          <div className="bg-muted/30 p-6 rounded-lg">
            <h3 className="font-semibold mb-2">Can I change plans anytime?</h3>
            <p className="text-sm text-muted-foreground">
              Yes! You can upgrade or downgrade your plan at any time. Changes
              take effect immediately, and we'll prorate any billing
              differences.
            </p>
          </div>

          <div className="bg-muted/30 p-6 rounded-lg">
            <h3 className="font-semibold mb-2">
              What happens if I exceed my search limit?
            </h3>
            <p className="text-sm text-muted-foreground">
              If you reach your daily search limit, you'll need to wait until
              the next day or upgrade to a higher plan. We'll notify you when
              you're approaching your limit.
            </p>
          </div>

          <div className="bg-muted/30 p-6 rounded-lg">
            <h3 className="font-semibold mb-2">
              Is there a free trial for paid plans?
            </h3>
            <p className="text-sm text-muted-foreground">
              Yes! Both Pro and Business plans come with a 7-day free trial. No
              credit card required to start your trial.
            </p>
          </div>

          <div className="bg-muted/30 p-6 rounded-lg">
            <h3 className="font-semibold mb-2">
              How accurate is the YouTube data?
            </h3>
            <p className="text-sm text-muted-foreground">
              We use the official YouTube Data API v3, so our data is as
              accurate and up-to-date as YouTube's own analytics. Data is
              refreshed in real-time.
            </p>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="text-center mt-16">
        <h2 className="text-2xl font-bold mb-4">Ready to grow your channel?</h2>
        <p className="text-muted-foreground mb-6">
          Join thousands of creators using data to inform their content
          strategy.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          {!isLoggedIn && (
            <Link href="/sign-up">
              <Button size="lg" className="px-8">
                Start Free Today
              </Button>
            </Link>
          )}
          <Link href="/about">
            <Button size="lg" variant="outline" className="px-8">
              Learn More
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
