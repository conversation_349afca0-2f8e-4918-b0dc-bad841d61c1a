import { forgotPasswordAction } from "@/app/actions";
import { FormMessage, Message } from "@/components/form-message";
import { SubmitButton } from "@/components/submit-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import { SmtpMessage } from "../smtp-message";

export default async function ForgotPassword(props: {
  searchParams: Promise<Message>;
}) {
  const searchParams = await props.searchParams;
  return (
    <div className="w-full items-center h-screen sm:max-w-md justify-center gap-2 px-4">
      <div className="bg-muted/30 p-4 rounded-lg">
        <Card className="w-full max-w-md">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl text-center">
              Reset Password
            </CardTitle>
          </CardHeader>
          <CardContent className="">
            <form className="flex-1 flex flex-col w-full gap-2 text-foreground [&>input]:mb-6 min-w-64 max-w-64 mx-auto">
              <div className="text-center text-sm text-muted-foreground">
                Already have an account?{" "}
                <Link
                  href="/sign-in"
                  className="text-primary font-medium hover:underline"
                >
                  Sign in
                </Link>
              </div>
              <div className="flex flex-col gap-2 [&>input]:mb-3 mt-8">
                <Label htmlFor="email">Email</Label>
                <Input name="email" required />
                <SubmitButton formAction={forgotPasswordAction}>
                  Reset Password
                </SubmitButton>
                <div className="pt-4">
                  <FormMessage message={searchParams} />
                </div>
              </div>
            </form>
            {/* <SmtpMessage /> */}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
