import { NextRequest, NextResponse } from "next/server";
import { getRateLimitInfo } from "@/lib/rate-limit";
import { createClient } from "@/utils/supabase/server";

export async function GET(request: NextRequest) {
  try {
    // Get user from session
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    // Get rate limit info
    const rateLimitInfo = await getRateLimitInfo(user.id);
    
    return NextResponse.json(rateLimitInfo);
  } catch (error: any) {
    console.error('Rate limit info API error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to get rate limit info' },
      { status: 500 }
    );
  }
}
