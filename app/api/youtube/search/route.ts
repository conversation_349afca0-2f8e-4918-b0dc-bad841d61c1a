import { NextRequest, NextResponse } from "next/server";
import { searchYouTubeVideos, SearchFilters } from "@/utils/youtube-api";

export async function GET(request: NextRequest) {
  try {
    // Authentication is handled by middleware
    // Get search parameters
    const searchParams = request.nextUrl.searchParams;
    const query = searchParams.get("query");

    if (!query) {
      return NextResponse.json(
        { error: "Search query is required" },
        { status: 400 }
      );
    }

    // Parse filters
    const filters: SearchFilters = {
      region: searchParams.get("region") || undefined,
      language: searchParams.get("language") || undefined,
      timeRange:
        (searchParams.get("timeRange") as SearchFilters["timeRange"]) ||
        undefined,
      maxResults: searchParams.get("maxResults")
        ? parseInt(searchParams.get("maxResults")!, 10)
        : undefined,
    };

    // Perform the search
    const videos = await searchYouTubeVideos(query, filters);

    return NextResponse.json({ videos });
  } catch (error: any) {
    console.error("YouTube search API error:", error);
    return NextResponse.json(
      { error: error.message || "Failed to search YouTube videos" },
      { status: 500 }
    );
  }
}
