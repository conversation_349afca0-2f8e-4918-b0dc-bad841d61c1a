import { NextRequest, NextResponse } from "next/server";
import { stripe, getUserTierFromSubscription } from "@/lib/stripe";
import { setUserTier } from "@/lib/rate-limit";
import { createClient } from "@/utils/supabase/server";

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const sessionId = searchParams.get('session_id');
    
    if (!sessionId) {
      return NextResponse.json(
        { error: 'No session ID provided' },
        { status: 400 }
      );
    }

    // Get user from session
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Retrieve the checkout session
    const session = await stripe.checkout.sessions.retrieve(sessionId);
    
    if (session.payment_status === 'paid' && session.subscription) {
      // Get the subscription
      const subscription = await stripe.subscriptions.retrieve(session.subscription as string);
      
      // Determine the user's new tier
      const newTier = getUserTierFromSubscription(subscription);
      
      // Update the user's tier in Redis
      await setUserTier(user.id, newTier);
      
      console.log(`Updated user ${user.id} to tier ${newTier} after successful payment`);
      
      return NextResponse.json({
        success: true,
        tier: newTier,
        message: `Successfully upgraded to ${newTier} plan!`
      });
    }

    return NextResponse.json(
      { error: 'Payment not completed or no subscription found' },
      { status: 400 }
    );
  } catch (error: any) {
    console.error('Payment success handler error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to process payment success' },
      { status: 500 }
    );
  }
}
