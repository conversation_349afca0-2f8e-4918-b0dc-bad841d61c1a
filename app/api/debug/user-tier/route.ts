import { NextRequest, NextResponse } from "next/server";
import { getUserTier, setUserTier } from "@/lib/rate-limit";
import { createClient } from "@/utils/supabase/server";

export async function GET(request: NextRequest) {
  try {
    // Get user from session
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const tier = await getUserTier(user.id);
    
    return NextResponse.json({
      userId: user.id,
      email: user.email,
      tier,
      userMetadata: user.user_metadata,
    });
  } catch (error: any) {
    console.error('Debug user tier error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to get user tier' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get user from session
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { tier } = await request.json();
    
    if (!['free', 'pro', 'business'].includes(tier)) {
      return NextResponse.json(
        { error: 'Invalid tier' },
        { status: 400 }
      );
    }

    await setUserTier(user.id, tier);
    
    return NextResponse.json({
      success: true,
      userId: user.id,
      newTier: tier,
    });
  } catch (error: any) {
    console.error('Debug set user tier error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to set user tier' },
      { status: 500 }
    );
  }
}
