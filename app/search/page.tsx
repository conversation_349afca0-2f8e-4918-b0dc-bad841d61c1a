"use client";

import { useState, useEffect } from "react";
import { createClient } from "@/utils/supabase/client";
import YouTubeSearch from "@/components/youtube-search";
import SearchHeader from "@/components/search-header";

export default function SearchPage() {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  useEffect(() => {
    const checkUser = async () => {
      const supabase = createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        window.location.href = "/sign-in";
        return;
      }

      setUser(user);
      setLoading(false);
    };

    checkUser();
  }, []);

  const handleSearchComplete = () => {
    setRefreshTrigger((prev) => prev + 1);
  };

  if (loading) {
    return (
      <div className="flex-1 w-full flex flex-col gap-8 py-8">
        <div className="max-w-4xl mx-auto w-full px-4">
          <div className="animate-pulse">
            <div className="h-8 bg-muted rounded w-64 mb-2"></div>
            <div className="h-4 bg-muted rounded w-96 mb-8"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="flex-1 w-full flex flex-col gap-8 py-8">
      <div className="max-w-4xl mx-auto w-full px-4">
        {/* Header with user info and tier badge */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold mb-2">YouTuber Insights</h1>
            <p className="text-muted-foreground">
              Find the top-performing videos in your niche to inspire your
              content strategy.
            </p>
          </div>
          <SearchHeader refreshTrigger={refreshTrigger} />
        </div>

        <YouTubeSearch onSearchComplete={handleSearchComplete} />
      </div>
    </div>
  );
}
