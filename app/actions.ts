"use server";

import { encodedRedirect } from "@/utils/utils";
import { createClient } from "@/utils/supabase/server";
import { headers } from "next/headers";
import { redirect } from "next/navigation";

export const signUpAction = async (formData: FormData) => {
  const supabase = await createClient();
  const origin = (await headers()).get("origin");

  const email = formData.get("email")?.toString();
  const password = formData.get("password")?.toString();
  const confirmPassword = formData.get("confirmPassword")?.toString();
  const firstName = formData.get("firstName")?.toString();
  const lastName = formData.get("lastName")?.toString();
  const termsAccepted = formData.get("terms");

  if (!email || !password || !confirmPassword || !firstName || !lastName) {
    return encodedRedirect(
      "error",
      "/sign-up",
      "Please fill all required fields."
    );
  }

  if (password !== confirmPassword) {
    return encodedRedirect("error", "/sign-up", "Passwords do not match.");
  }

  if (!termsAccepted) {
    return encodedRedirect(
      "error",
      "/sign-up",
      "You must agree to the terms and privacy policy."
    );
  }

  // Try to sign in with a fake password to check if the email exists
  const { error: signInError } = await supabase.auth.signInWithPassword({
    email,
    password: "fake-password-to-check-existence",
  });

  // If the error is not about invalid credentials, the email might not exist
  // If it's about invalid credentials, the email exists
  if (
    signInError &&
    signInError.message.includes("Invalid login credentials")
  ) {
    return encodedRedirect(
      "error",
      "/sign-up",
      "Email already in use. Please use a different email or sign in."
    );
  }

  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      emailRedirectTo: `${origin}/auth/callback`,
      data: {
        firstName,
        lastName,
      },
    },
  });

  if (error) {
    console.error(error.code, error.message);
    return encodedRedirect("error", "/sign-up", error.message);
  }

  // Check if the user already exists but is just not confirmed
  if (data?.user && data.user.identities && data.user.identities.length === 0) {
    return encodedRedirect(
      "error",
      "/sign-up",
      "Email already in use. Please check your email for the verification link or try to sign in."
    );
  }

  return encodedRedirect(
    "success",
    "/sign-up",
    "Thanks for signing up! Please check your email for a verification link."
  );
};

export const signInAction = async (formData: FormData) => {
  const email = formData.get("email") as string;
  const password = formData.get("password") as string;
  const supabase = await createClient();

  const { error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (error) {
    return encodedRedirect("error", "/sign-in", error.message);
  }

  return redirect("/search");
};

export const forgotPasswordAction = async (formData: FormData) => {
  const email = formData.get("email")?.toString();
  const supabase = await createClient();
  const origin = (await headers()).get("origin");
  const callbackUrl = formData.get("callbackUrl")?.toString();

  if (!email) {
    return encodedRedirect("error", "/forgot-password", "Email is required");
  }

  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${origin}/auth/callback?redirect_to=/search/reset-password`,
  });

  if (error) {
    console.error(error.message);
    return encodedRedirect(
      "error",
      "/forgot-password",
      "Could not reset password"
    );
  }

  if (callbackUrl) {
    return redirect(callbackUrl);
  }

  return encodedRedirect(
    "success",
    "/forgot-password",
    "Check your email for a link to reset your password."
  );
};

export const resetPasswordAction = async (formData: FormData) => {
  const supabase = await createClient();

  const password = formData.get("password") as string;
  const confirmPassword = formData.get("confirmPassword") as string;

  if (!password || !confirmPassword) {
    return encodedRedirect(
      "error",
      "/search/reset-password",
      "Password and confirm password are required"
    );
  }

  if (password !== confirmPassword) {
    return encodedRedirect(
      "error",
      "/search/reset-password",
      "Passwords do not match"
    );
  }

  const { error } = await supabase.auth.updateUser({
    password: password,
  });

  if (error) {
    return encodedRedirect(
      "error",
      "/search/reset-password",
      "Password update failed"
    );
  }

  return encodedRedirect(
    "success",
    "/search/reset-password",
    "Password updated"
  );
};

export const signOutAction = async () => {
  const supabase = await createClient();
  await supabase.auth.signOut();
  return redirect("/sign-in");
};

export const signInWithGoogleAction = async () => {
  const supabase = await createClient();
  const origin = (await headers()).get("origin");

  const { data, error } = await supabase.auth.signInWithOAuth({
    provider: "google",
    options: {
      redirectTo: `${origin}/auth/callback`,
      queryParams: {
        access_type: "offline",
        prompt: "consent",
      },
    },
  });

  if (error) {
    console.error(error);
    return encodedRedirect("error", "/sign-in", error.message);
  }

  return redirect(data.url);
};
