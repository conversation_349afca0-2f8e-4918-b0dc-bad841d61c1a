import Link from "next/link";

export default function TermsOfService() {
  return (
    <div className="container mx-auto py-12 px-4 max-w-4xl">
      <h1 className="text-3xl font-bold mb-8">Terms of Service</h1>
      
      <div className="prose prose-sm sm:prose lg:prose-lg dark:prose-invert">
        <p className="text-muted-foreground mb-6">
          Last Updated: May 21, 2025
        </p>
        
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">1. Introduction</h2>
          <p>
            Welcome to YouTuber Insights. These Terms of Service ("Terms") govern your access to and use of the YouTuber Insights website and services (collectively, the "Service"). By accessing or using the Service, you agree to be bound by these Terms.
          </p>
        </section>
        
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">2. Using Our Service</h2>
          
          <h3 className="text-xl font-medium mb-2">2.1 Account Registration</h3>
          <p>
            To access certain features of the Service, you may need to register for an account. You agree to provide accurate, current, and complete information during the registration process and to update such information to keep it accurate, current, and complete.
          </p>
          
          <h3 className="text-xl font-medium mb-2">2.2 Account Security</h3>
          <p>
            You are responsible for safeguarding your password and for all activities that occur under your account. You agree to notify us immediately of any unauthorized use of your account.
          </p>
          
          <h3 className="text-xl font-medium mb-2">2.3 Age Restrictions</h3>
          <p>
            The Service is intended for users who are at least 13 years of age. By using the Service, you confirm that you are at least 13 years old.
          </p>
        </section>
        
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">3. YouTube Data API Services</h2>
          <p>
            Our Service uses the YouTube Data API v3 to provide video insights. By using our Service, you acknowledge and agree that:
          </p>
          <ul className="list-disc pl-6 mb-4">
            <li>
              You are also bound by{" "}
              <a href="https://www.youtube.com/t/terms" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">
                YouTube's Terms of Service
              </a>.
            </li>
            <li>
              We do not guarantee the accuracy, completeness, or timeliness of data obtained from YouTube.
            </li>
            <li>
              You will not use our Service to scrape, mine, or gather large amounts of data from YouTube beyond what is reasonably necessary for your personal, non-commercial use.
            </li>
            <li>
              You will not use our Service to attempt to identify individual YouTube users or collect personal information about them.
            </li>
          </ul>
        </section>
        
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">4. Acceptable Use</h2>
          <p>
            You agree not to use the Service to:
          </p>
          <ul className="list-disc pl-6 mb-4">
            <li>Violate any applicable laws or regulations</li>
            <li>Infringe the intellectual property rights of others</li>
            <li>Transmit any material that is harmful, threatening, abusive, or otherwise objectionable</li>
            <li>Impersonate any person or entity or falsely state your affiliation with a person or entity</li>
            <li>Interfere with or disrupt the Service or servers or networks connected to the Service</li>
            <li>Attempt to gain unauthorized access to any part of the Service</li>
            <li>Use the Service for any commercial purpose without our prior written consent</li>
          </ul>
        </section>
        
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">5. Intellectual Property</h2>
          <p>
            The Service and its original content, features, and functionality are owned by YouTuber Insights and are protected by international copyright, trademark, patent, trade secret, and other intellectual property or proprietary rights laws.
          </p>
        </section>
        
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">6. Termination</h2>
          <p>
            We may terminate or suspend your account and access to the Service immediately, without prior notice or liability, for any reason, including if you breach these Terms.
          </p>
        </section>
        
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">7. Limitation of Liability</h2>
          <p>
            In no event shall YouTuber Insights, its directors, employees, partners, agents, suppliers, or affiliates be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from your access to or use of or inability to access or use the Service.
          </p>
        </section>
        
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">8. Changes to Terms</h2>
          <p>
            We reserve the right to modify or replace these Terms at any time. If a revision is material, we will provide at least 30 days' notice prior to any new terms taking effect. What constitutes a material change will be determined at our sole discretion.
          </p>
        </section>
        
        {/* <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">9. Contact Us</h2>
          <p>
            If you have any questions about these Terms, please contact us at:
          </p>
          <p className="mt-2">
            Email: <EMAIL>
          </p>
        </section> */}
      </div>
      
      <div className="mt-12 text-center">
        <Link href="/" className="text-primary hover:underline">
          Return to Home
        </Link>
      </div>
    </div>
  );
}
