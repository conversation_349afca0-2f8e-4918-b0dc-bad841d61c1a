"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  TrendingUp,
  Search,
  BarChart3,
  Download,
  Users,
  Target,
  Lightbulb,
} from "lucide-react";
import { createClient } from "@/utils/supabase/client";
import { useEffect, useState } from "react";

export default function About() {
  const [isLoggedIn, setIsLoggedIn] = useState<boolean>(false);

  useEffect(() => {
    const checkUser = async () => {
      const supabase = createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();
      setIsLoggedIn(!!user);
    };

    checkUser();
  }, []);
  return (
    <div className="container mx-auto py-12 px-4 max-w-4xl">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">About YouTuber Insights</h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Empowering content creators with data-driven insights to grow their
          YouTube channels faster and smarter.
        </p>
      </div>

      {/* Mission Section */}
      <section className="mb-16">
        <div className="grid md:grid-cols-2 gap-8 items-center">
          <div>
            <h2 className="text-3xl font-bold mb-4">Our Mission</h2>
            <p className="text-lg text-muted-foreground mb-6">
              We believe every content creator deserves access to the insights
              that can transform their channel. YouTuber Insights was born from
              the frustration of spending countless hours researching what
              works, only to find scattered and unreliable information.
            </p>
            <p className="text-lg text-muted-foreground">
              Our platform democratizes access to YouTube performance data,
              giving creators of all sizes the same competitive intelligence
              that top channels use to dominate their niches.
            </p>
          </div>
          <div className="bg-primary/5 p-8 rounded-lg">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-primary">5K+</div>
                <div className="text-sm text-muted-foreground">
                  Active Users
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary">100K+</div>
                <div className="text-sm text-muted-foreground">
                  Videos Analyzed
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary">50+</div>
                <div className="text-sm text-muted-foreground">Countries</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary">24/7</div>
                <div className="text-sm text-muted-foreground">
                  Data Updates
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="mb-16">
        <h2 className="text-3xl font-bold text-center mb-12">
          How YouTuber Insights Works
        </h2>
        <div className="grid md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Search className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-xl font-semibold mb-3">1. Search Your Niche</h3>
            <p className="text-muted-foreground">
              Enter any topic or keyword related to your content niche. Our
              system searches across millions of YouTube videos.
            </p>
          </div>
          <div className="text-center">
            <div className="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <BarChart3 className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-xl font-semibold mb-3">
              2. Analyze Performance
            </h3>
            <p className="text-muted-foreground">
              We analyze key metrics like views, engagement rates, subscriber
              counts, and publish dates to rank the top performers.
            </p>
          </div>
          <div className="text-center">
            <div className="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Download className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-xl font-semibold mb-3">3. Export & Apply</h3>
            <p className="text-muted-foreground">
              Download your insights in CSV or TXT format and use the data to
              inform your content strategy and video creation.
            </p>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="mb-16">
        <h2 className="text-3xl font-bold text-center mb-12">
          Why Creators Choose Us
        </h2>
        <div className="grid md:grid-cols-2 gap-8">
          <div className="flex gap-4">
            <div className="bg-green-100 dark:bg-green-900/20 w-12 h-12 rounded-lg flex items-center justify-center flex-shrink-0">
              <TrendingUp className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-2">Real-Time Data</h3>
              <p className="text-muted-foreground">
                Access the most current YouTube performance data through
                official YouTube Data API v3 integration.
              </p>
            </div>
          </div>
          <div className="flex gap-4">
            <div className="bg-blue-100 dark:bg-blue-900/20 w-12 h-12 rounded-lg flex items-center justify-center flex-shrink-0">
              <Target className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-2">
                Niche-Specific Insights
              </h3>
              <p className="text-muted-foreground">
                Filter by region, language, and time range to get insights that
                are relevant to your specific audience.
              </p>
            </div>
          </div>
          <div className="flex gap-4">
            <div className="bg-purple-100 dark:bg-purple-900/20 w-12 h-12 rounded-lg flex items-center justify-center flex-shrink-0">
              <Users className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-2">Engagement Metrics</h3>
              <p className="text-muted-foreground">
                Go beyond view counts with detailed engagement rates,
                like-to-view ratios, and comment analysis.
              </p>
            </div>
          </div>
          <div className="flex gap-4">
            <div className="bg-orange-100 dark:bg-orange-900/20 w-12 h-12 rounded-lg flex items-center justify-center flex-shrink-0">
              <Lightbulb className="h-6 w-6 text-orange-600" />
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-2">
                Actionable Intelligence
              </h3>
              <p className="text-muted-foreground">
                Get clear, actionable insights that you can immediately apply to
                your content strategy and video planning.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="mb-16">
        <h2 className="text-3xl font-bold text-center mb-8">
          Built by Creators, for Creators
        </h2>
        <div className="bg-muted/30 p-8 rounded-lg text-center">
          <p className="text-lg text-muted-foreground mb-6">
            YouTuber Insights was created by a team of content creators and data
            scientists who understand the challenges of growing a YouTube
            channel in today's competitive landscape.
          </p>
          <p className="text-lg text-muted-foreground">
            We've been where you are – staring at analytics, wondering what
            content to create next, and trying to decode the algorithm. That's
            why we built the tool we wished we had when we started.
          </p>
        </div>
      </section>

      {/* CTA Section */}
      <section className="text-center">
        <h2 className="text-3xl font-bold mb-4">Ready to Grow Your Channel?</h2>
        <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
          Join thousands of creators who are using data-driven insights to
          create better content and grow their audience faster.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          {!isLoggedIn && (
            <Link href="/sign-up">
              <Button size="lg" className="px-8">
                Start Free Today
              </Button>
            </Link>
          )}
          <Link href="/pricing">
            <Button size="lg" variant="outline" className="px-8">
              View Pricing
            </Button>
          </Link>
        </div>
      </section>
    </div>
  );
}
