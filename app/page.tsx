import Hero from "@/components/hero";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import TrendingSearches from "@/components/trending-searches";
import { Search, TrendingUp, Shield, Users, Lock } from "lucide-react";
import Link from "next/link";

export default async function Home() {
  return (
    <>
      <Hero />
      <main className="flex-1 flex flex-col gap-6 px-4">
        {/* Search Bar */}
        <div className="max-w-2xl mx-auto mb-8">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
            <Input
              type="text"
              placeholder="Enter a topic (e.g., productivity, hair care, python tutorials)"
              className="pl-10 py-6 text-lg"
            />
            <Button className="absolute right-1 top-1/2 transform -translate-y-1/2">
              Search
            </Button>
          </div>
          <TrendingSearches />
        </div>

        {/* Trust Indicators */}
        <div className="flex flex-col md:flex-row items-center justify-center gap-4 md:gap-8 mt-8 text-sm text-muted-foreground">
          <div className="flex items-center">
            <Shield className="h-5 w-5 mr-2 text-green-600" />
            <span>Scamadviser Verified</span>
          </div>
          <div className="flex items-center">
            <Users className="h-5 w-5 mr-2 text-blue-600" />
            <span>Trusted by 5,000+ users</span>
          </div>
          <div className="flex items-center">
            <Lock className="h-5 w-5 mr-2 text-yellow-600" />
            <span>SSL Encrypted</span>
          </div>
        </div>

        {/* Features Section */}
        <section className="bg-muted/50 py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">
              How YouTuber Insights Works
            </h2>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="bg-card p-6 rounded-lg shadow-sm">
                <div className="bg-primary/10 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                  <Search className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-2">
                  Search Your Niche
                </h3>
                <p className="text-muted-foreground">
                  Enter any topic to discover what's working right now in your
                  content category.
                </p>
              </div>
              <div className="bg-card p-6 rounded-lg shadow-sm">
                <div className="bg-primary/10 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                  <TrendingUp className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-2">
                  Analyze Top Performers
                </h3>
                <p className="text-muted-foreground">
                  Get detailed metrics on the top 10 videos including views,
                  engagement, and subscriber growth.
                </p>
              </div>
              <div className="bg-card p-6 rounded-lg shadow-sm">
                <div className="bg-primary/10 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                  <svg
                    className="h-6 w-6 text-primary"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                    />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold mb-2">Export Insights</h3>
                <p className="text-muted-foreground">
                  Save your research as TXT or CSV to reference later or share
                  with your team.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto">
              <h2 className="text-3xl font-bold text-center mb-12">
                Why Content Creators Love Us
              </h2>
              <div className="space-y-8">
                <div className="flex gap-4">
                  <div className="flex-shrink-0 w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                    <svg
                      className="h-6 w-6 text-green-600"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-1">
                      Save Hours of Research Time
                    </h3>
                    <p className="text-muted-foreground">
                      Stop watching endless videos trying to figure out what
                      works. Get data-backed insights in seconds.
                    </p>
                  </div>
                </div>
                <div className="flex gap-4">
                  <div className="flex-shrink-0 w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                    <svg
                      className="h-6 w-6 text-blue-600"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-1">
                      Grow Your Channel Faster
                    </h3>
                    <p className="text-muted-foreground">
                      Learn from successful creators in your niche and apply
                      their winning strategies to your content.
                    </p>
                  </div>
                </div>
                <div className="flex gap-4">
                  <div className="flex-shrink-0 w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center">
                    <svg
                      className="h-6 w-6 text-purple-600"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-1">
                      Make Data-Driven Decisions
                    </h3>
                    <p className="text-muted-foreground">
                      Stop guessing what might work. Base your content strategy
                      on real performance data.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="bg-muted/50 py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">
              Frequently Asked Questions
            </h2>
            <div className="max-w-3xl mx-auto space-y-6">
              <div className="bg-card p-6 rounded-lg shadow-sm">
                <h3 className="text-xl font-semibold mb-2">
                  How accurate is the data?
                </h3>
                <p className="text-muted-foreground">
                  We use the official YouTube Data API v3 to fetch the most
                  up-to-date metrics directly from YouTube's servers.
                </p>
              </div>
              <div className="bg-card p-6 rounded-lg shadow-sm">
                <h3 className="text-xl font-semibold mb-2">
                  How many searches can I perform?
                </h3>
                <p className="text-muted-foreground">
                  Free accounts get 10 searches per month. Premium subscribers
                  get unlimited searches and additional filtering options.
                </p>
              </div>
              <div className="bg-card p-6 rounded-lg shadow-sm">
                <h3 className="text-xl font-semibold mb-2">
                  Can I filter by specific metrics?
                </h3>
                <p className="text-muted-foreground">
                  Yes, you can filter by time range, language, region, and sort
                  by different metrics like views, engagement rate, or
                  subscriber count.
                </p>
              </div>
              <div className="bg-card p-6 rounded-lg shadow-sm">
                <h3 className="text-xl font-semibold mb-2">
                  How do I export the data?
                </h3>
                <p className="text-muted-foreground">
                  After your search results appear, simply click the "Export"
                  button and choose between TXT or CSV format.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-primary/5">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold mb-6">
              Ready to Grow Your YouTube Channel?
            </h2>
            <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
              Join thousands of content creators who are using data to inform
              their content strategy and grow their audience.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/sign-up">
                <Button size="lg" className="px-8">
                  Sign Up Free
                </Button>
              </Link>
              <Button size="lg" variant="outline" className="px-8">
                See How It Works
              </Button>
            </div>
          </div>
        </section>
      </main>
    </>
  );
}
