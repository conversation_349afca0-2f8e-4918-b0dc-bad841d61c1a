import { type NextRequest, NextResponse } from "next/server";
import { updateSession } from "@/utils/supabase/middleware";
import { checkRateLimit } from "@/lib/rate-limit";
import { createClient } from "@/utils/supabase/server";

export async function middleware(request: NextRequest) {
  // Handle rate limiting for API routes
  if (request.nextUrl.pathname.startsWith("/api/youtube/search")) {
    try {
      // Get user from session
      const supabase = await createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        return NextResponse.json(
          { error: "Authentication required" },
          { status: 401 }
        );
      }

      // Check rate limit
      const rateLimitResult = await checkRateLimit(user.id);

      if (!rateLimitResult.success) {
        return NextResponse.json(
          {
            error: "Rate limit exceeded",
            limit: rateLimitResult.limit,
            remaining: rateLimitResult.remaining,
            reset: rateLimitResult.reset,
            tier: rateLimitResult.tier,
          },
          {
            status: 429,
            headers: {
              "X-RateLimit-Limit": rateLimitResult.limit.toString(),
              "X-RateLimit-Remaining": rateLimitResult.remaining.toString(),
              "X-RateLimit-Reset":
                rateLimitResult.reset instanceof Date
                  ? rateLimitResult.reset.getTime().toString()
                  : rateLimitResult.reset.toString(),
              "X-RateLimit-Tier": rateLimitResult.tier,
            },
          }
        );
      }

      // Add rate limit headers to successful requests
      const response = NextResponse.next();
      response.headers.set(
        "X-RateLimit-Limit",
        rateLimitResult.limit.toString()
      );
      response.headers.set(
        "X-RateLimit-Remaining",
        rateLimitResult.remaining.toString()
      );
      response.headers.set(
        "X-RateLimit-Reset",
        rateLimitResult.reset instanceof Date
          ? rateLimitResult.reset.getTime().toString()
          : rateLimitResult.reset.toString()
      );
      response.headers.set("X-RateLimit-Tier", rateLimitResult.tier);

      return response;
    } catch (error) {
      console.error("Rate limiting error:", error);
      // On error, allow the request to proceed
      return NextResponse.next();
    }
  }

  // Handle session updates for other routes
  return await updateSession(request);
}

export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - images - .svg, .png, .jpg, .jpeg, .gif, .webp
     * Feel free to modify this pattern to include more paths.
     */
    "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};
