# Supabase configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# Google OAuth configuration
# You can get these from the Google Cloud Console (https://console.cloud.google.com/)
# 1. Create a new project
# 2. Go to "APIs & Services" > "Credentials"
# 3. Create an OAuth client ID (Web application)
# 4. Add authorized redirect URIs:
#    - http://localhost:3000/auth/callback (for local development)
#    - https://your-production-domain.com/auth/callback (for production)

# YouTube Data API v3
# Get your API key from Google Cloud Console:
# 1. Go to https://console.cloud.google.com/
# 2. Create a project (or use an existing one)
# 3. Enable the YouTube Data API v3
# 4. Create an API key in the Credentials section
YOUTUBE_API_KEY=your-youtube-api-key

# Stripe Configuration
# Get your API keys from Stripe Dashboard (https://dashboard.stripe.com/apikeys)
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# Stripe Price IDs (create these in your Stripe dashboard)
STRIPE_PRO_PRICE_ID=price_your-pro-price-id
STRIPE_BUSINESS_PRICE_ID=price_your-business-price-id

# Rate Limiting with Upstash Redis
# Get your Redis URL and token from Upstash (https://upstash.com/)
# 1. Create a Redis database
# 2. Copy the REST URL and token
UPSTASH_REDIS_REST_URL=your-upstash-redis-url
UPSTASH_REDIS_REST_TOKEN=your-upstash-redis-token
