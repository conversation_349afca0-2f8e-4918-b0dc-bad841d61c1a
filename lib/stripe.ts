import Stripe from "stripe";

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error("STRIPE_SECRET_KEY is not set");
}

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: "2024-12-18.acacia",
  typescript: true,
});

// Product and price configuration
export const STRIPE_CONFIG = {
  products: {
    pro: {
      name: "YouTuber Insights Pro",
      description: "Perfect for serious content creators",
      priceId: process.env.STRIPE_PRO_PRICE_ID!,
      amount: 1000, // $10.00 in cents
    },
    business: {
      name: "YouTuber Insights Business",
      description: "For teams and agencies",
      priceId: process.env.STRIPE_BUSINESS_PRICE_ID!,
      amount: 4900, // $49.00 in cents
    },
  },
};

// Helper function to create a checkout session
export async function createCheckoutSession({
  priceId,
  userId,
  userEmail,
  successUrl,
  cancelUrl,
}: {
  priceId: string;
  userId: string;
  userEmail: string;
  successUrl: string;
  cancelUrl: string;
}) {
  try {
    const session = await stripe.checkout.sessions.create({
      mode: "subscription",
      payment_method_types: ["card"],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      success_url: successUrl,
      cancel_url: cancelUrl,
      customer_email: userEmail,
      metadata: {
        userId,
      },
      subscription_data: {
        metadata: {
          userId,
        },
      },
      allow_promotion_codes: true,
      billing_address_collection: "auto",
    });

    return session;
  } catch (error) {
    console.error("Error creating checkout session:", error);
    throw error;
  }
}

// Helper function to create a customer portal session
export async function createCustomerPortalSession({
  customerId,
  returnUrl,
}: {
  customerId: string;
  returnUrl: string;
}) {
  try {
    const session = await stripe.billingPortal.sessions.create({
      customer: customerId,
      return_url: returnUrl,
      configuration: await getOrCreatePortalConfiguration(),
    });

    return session;
  } catch (error) {
    console.error("Error creating customer portal session:", error);
    throw error;
  }
}

// Helper function to get or create portal configuration
async function getOrCreatePortalConfiguration() {
  try {
    // Try to get existing configurations
    const configurations = await stripe.billingPortal.configurations.list({
      limit: 1,
    });

    if (configurations.data.length > 0) {
      return configurations.data[0].id;
    }

    // Create a new configuration if none exists
    const configuration = await stripe.billingPortal.configurations.create({
      business_profile: {
        headline: "Manage your YouTuber Insights subscription",
      },
      features: {
        payment_method_update: {
          enabled: true,
        },
        subscription_cancel: {
          enabled: true,
          mode: "at_period_end",
        },
        subscription_update: {
          enabled: true,
          default_allowed_updates: ["price"],
          proration_behavior: "create_prorations",
        },
        invoice_history: {
          enabled: true,
        },
      },
    });

    return configuration.id;
  } catch (error) {
    console.error("Error getting or creating portal configuration:", error);
    // Return undefined to let Stripe use default configuration
    return undefined;
  }
}

// Helper function to get subscription details
export async function getSubscription(subscriptionId: string) {
  try {
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    return subscription;
  } catch (error) {
    console.error("Error retrieving subscription:", error);
    throw error;
  }
}

// Helper function to cancel subscription
export async function cancelSubscription(subscriptionId: string) {
  try {
    const subscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: true,
    });
    return subscription;
  } catch (error) {
    console.error("Error canceling subscription:", error);
    throw error;
  }
}

// Helper function to get customer by email
export async function getCustomerByEmail(email: string) {
  try {
    const customers = await stripe.customers.list({
      email,
      limit: 1,
    });

    return customers.data[0] || null;
  } catch (error) {
    console.error("Error getting customer by email:", error);
    throw error;
  }
}

// Helper function to determine user tier from subscription
export function getUserTierFromSubscription(
  subscription: any
): "free" | "pro" | "business" {
  if (!subscription || subscription.status !== "active") {
    return "free";
  }

  // Get the price ID from the subscription
  const priceId = subscription.items.data[0]?.price?.id;

  if (priceId === STRIPE_CONFIG.products.pro.priceId) {
    return "pro";
  } else if (priceId === STRIPE_CONFIG.products.business.priceId) {
    return "business";
  }

  return "free";
}
