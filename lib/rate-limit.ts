import { Ratelimit } from "@upstash/ratelimit";
import { Redis } from "@upstash/redis";

// Create Redis instance
const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!,
});

// Define rate limits for different user tiers
export const rateLimits = {
  free: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(5, "1 d"), // 5 requests per day
    analytics: true,
    prefix: "ratelimit:free",
  }),
  pro: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(100, "1 d"), // 100 requests per day
    analytics: true,
    prefix: "ratelimit:pro",
  }),
  business: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(10000, "1 d"), // Effectively unlimited (10k per day)
    analytics: true,
    prefix: "ratelimit:business",
  }),
};

// User tier type
export type UserTier = "free" | "pro" | "business";

// Function to get user's subscription tier
export async function getUserTier(userId: string): Promise<UserTier> {
  try {
    const userTierData = await redis.get(`user:${userId}:tier`);

    if (userTierData && typeof userTierData === "string") {
      return userTierData as UserTier;
    }

    // If no tier is set, default to free and set it
    await setUserTier(userId, "free");
    return "free";
  } catch (error) {
    console.error("Error getting user tier:", error);
    return "free"; // Default to free tier on error
  }
}

// Function to set user tier (for when they subscribe/upgrade)
export async function setUserTier(
  userId: string,
  tier: UserTier
): Promise<void> {
  try {
    await redis.set(`user:${userId}:tier`, tier);
  } catch (error) {
    console.error("Error setting user tier:", error);
  }
}

// Function to track usage
// export async function trackUsage(userId: string, userTier?: UserTier) {
//   try {
//     const tier = userTier || (await getUserTier(userId));
//     const usageKey = `usage:${tier}:${userId}`;

//     // Get current usage
//     const currentUsage = await redis.get(usageKey);
//     let newUsage = 1;

//     if (currentUsage && typeof currentUsage === "number") {
//       newUsage = currentUsage + 1;
//     }

//     // Set usage with 24 hour expiry
//     await redis.set(usageKey, newUsage, { ex: 24 * 60 * 60 });

//     return newUsage;
//   } catch (error) {
//     console.error("Error tracking usage:", error);
//     return 0;
//   }
// }
export async function trackUsage(userId: string, userTier?: UserTier) {
  try {
    const tier = userTier || (await getUserTier(userId));
    const usageKey = `usage:${userId}`; // Tier-independent key

    // Increment usage atomically and set TTL if key is new
    const currentUsage = await redis.incr(usageKey);

    // Set expiration if it's the first use of the day
    if (currentUsage === 1) {
      await redis.expire(usageKey, 24 * 60 * 60); // 1 day in seconds
    }

    return currentUsage;
  } catch (error) {
    console.error("Error tracking usage:", error);
    return 0;
  }
}

// Function to check rate limit for a user
export async function checkRateLimit(userId: string, userTier?: UserTier) {
  try {
    // Get user tier if not provided
    const tier = userTier || (await getUserTier(userId));

    // Get the appropriate rate limiter
    const rateLimit = rateLimits[tier];

    // Check the rate limit
    const result = await rateLimit.limit(userId);

    // If successful, track the usage
    if (result.success) {
      await trackUsage(userId, tier);
    }

    return {
      success: result.success,
      limit: result.limit,
      remaining: result.remaining,
      reset: result.reset,
      tier,
    };
  } catch (error) {
    console.error("Rate limit check error:", error);
    // On error, allow the request but log it
    return {
      success: true,
      limit: 0,
      remaining: 0,
      reset: new Date(),
      tier: "free" as UserTier,
    };
  }
}

// Function to get rate limit info without consuming a request
// export async function getRateLimitInfo(userId: string, userTier?: UserTier) {
//   try {
//     const tier = userTier || (await getUserTier(userId));

//     const limits = {
//       free: 5,
//       pro: 100,
//       business: 10000,
//     };

//     // Use a separate tracking key to store actual usage
//     const usageKey = `usage:${tier}:${userId}`;
//     const usageData = await redis.get(usageKey);

//     let used = 0;
//     if (usageData && typeof usageData === "number") {
//       used = usageData;
//     } else if (usageData && Array.isArray(usageData)) {
//       // Count requests in the current window (24 hours)
//       const now = Date.now();
//       const windowSize = 24 * 60 * 60 * 1000; // 1 day in milliseconds
//       const windowStart = now - windowSize;

//       used = usageData.filter(
//         (timestamp: number) => timestamp > windowStart
//       ).length;
//     }

//     const remaining = Math.max(0, limits[tier] - used);

//     return {
//       tier,
//       limit: limits[tier],
//       used,
//       remaining,
//     };
//   } catch (error) {
//     console.error("Error getting rate limit info:", error);
//     return {
//       tier: "free" as UserTier,
//       limit: 5,
//       used: 0,
//       remaining: 5,
//     };
//   }
// }
export async function getRateLimitInfo(userId: string, userTier?: UserTier) {
  try {
    const tier = userTier || (await getUserTier(userId));

    const limits: Record<UserTier, number> = {
      free: 5,
      pro: 100,
      business: 10000,
    };

    const usageKey = `usage:${userId}`;
    const usageRaw = await redis.get(usageKey);

    let used = 0;
    if (usageRaw !== null && !isNaN(Number(usageRaw))) {
      used = Number(usageRaw);
    }

    const remaining = Math.max(0, limits[tier] - used);

    return {
      tier,
      limit: limits[tier],
      used,
      remaining,
    };
  } catch (error) {
    console.error("Error getting rate limit info:", error);
    return {
      tier: "free" as UserTier,
      limit: 5,
      used: 0,
      remaining: 5,
    };
  }
}
