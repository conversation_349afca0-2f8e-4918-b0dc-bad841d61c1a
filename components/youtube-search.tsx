"use client";

import { useState, FormEvent } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import TrendingSearches from "@/components/trending-searches";
import { YouTubeVideo, SearchFilters } from "@/utils/youtube-api";
import VideoResults from "@/components/video-results";
import SearchFiltersComponent from "@/components/search-filters";
import RateLimitDisplay from "@/components/rate-limit-display";
import { useRouter } from "next/navigation";

export default function YouTubeSearch() {
  const [query, setQuery] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [videos, setVideos] = useState<YouTubeVideo[]>([]);
  const [filters, setFilters] = useState<SearchFilters>({
    region: "US",
    language: "en",
    timeRange: "month",
    maxResults: 10,
  });
  const [hasSearched, setHasSearched] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const router = useRouter();

  const handleSearch = async (e: FormEvent) => {
    e.preventDefault();

    if (!query.trim()) {
      setError("Please enter a search term");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Build query string with filters
      const queryParams = new URLSearchParams({
        query: query.trim(),
        ...(filters.region && { region: filters.region }),
        ...(filters.language && { language: filters.language }),
        ...(filters.timeRange && { timeRange: filters.timeRange }),
        ...(filters.maxResults && {
          maxResults: filters.maxResults.toString(),
        }),
      });

      const response = await fetch(
        `/api/youtube/search?${queryParams.toString()}`
      );

      if (!response.ok) {
        const errorData = await response.json();
        if (response.status === 429) {
          // Rate limit exceeded - redirect to pricing page with message
          // const remaining = errorData.remaining || 0;
          // router.push(
          //   `/pricing?message=${encodeURIComponent(`You have ${remaining} searches remaining. Upgrade plan for more searches.`)}`
          // );
          router.push(
            `/pricing?message=${encodeURIComponent(`You have exhausted your searches for today. Kindly upgrade your plan to search.`)}`
          );
          return;
        }
        throw new Error(errorData.error || "Failed to search videos");
      }

      const data = await response.json();
      setVideos(data.videos);
      setHasSearched(true);

      // Refresh the rate limit display
      setRefreshKey((prev) => prev + 1);
    } catch (err: any) {
      setError(err.message || "An error occurred while searching");
      setVideos([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTopicSelect = (topic: string) => {
    setQuery(topic);
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      <div className="mb-6">
        <RateLimitDisplay refreshTrigger={refreshKey} />
      </div>

      <form onSubmit={handleSearch} className="mb-8">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
          <Input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Enter a topic (e.g., productivity, hair care, python tutorials)"
            className="pl-10 py-6 text-lg"
          />
          <Button
            type="submit"
            className="absolute right-1 top-1/2 transform -translate-y-1/2"
            disabled={isLoading}
          >
            {isLoading ? "Searching..." : "Search"}
          </Button>
        </div>

        <div className="mt-4">
          <SearchFiltersComponent filters={filters} setFilters={setFilters} />
        </div>

        <TrendingSearches onTopicSelect={handleTopicSelect} />
      </form>

      {error && (
        <div className="bg-destructive/10 text-destructive p-4 rounded-md mb-6">
          {error}
        </div>
      )}

      {hasSearched && (
        <VideoResults
          videos={videos}
          isLoading={isLoading}
          searchTerm={query}
        />
      )}
    </div>
  );
}
