"use client";

import { useState, FormEvent, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import TrendingSearches from "@/components/trending-searches";
import { YouTubeVideo, SearchFilters } from "@/utils/youtube-api";
import VideoResults from "@/components/video-results";
import SearchFiltersComponent from "@/components/search-filters";
import { useRouter, useSearchParams } from "next/navigation";

interface YouTubeSearchProps {
  onSearchComplete?: () => void;
}

export default function YouTubeSearch({
  onSearchComplete,
}: YouTubeSearchProps) {
  const [query, setQuery] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [videos, setVideos] = useState<YouTubeVideo[]>([]);
  const [filters, setFilters] = useState<SearchFilters>({
    region: "US",
    language: "en",
    timeRange: "month",
    maxResults: 10,
  });
  const [hasSearched, setHasSearched] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();

  // Handle pre-populated query from URL or session storage
  useEffect(() => {
    // Check URL params first
    const urlQuery = searchParams.get("q");
    if (urlQuery) {
      setQuery(urlQuery);
      return;
    }

    // Check session storage for pending search query
    const pendingQuery = sessionStorage.getItem("pendingSearchQuery");
    if (pendingQuery) {
      setQuery(pendingQuery);
      sessionStorage.removeItem("pendingSearchQuery");
    }
  }, [searchParams]);

  const handleSearch = async (e: FormEvent) => {
    e.preventDefault();

    if (!query.trim()) {
      setError("Please enter a search term");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Build query string with filters
      const queryParams = new URLSearchParams({
        query: query.trim(),
        ...(filters.region && { region: filters.region }),
        ...(filters.language && { language: filters.language }),
        ...(filters.timeRange && { timeRange: filters.timeRange }),
        ...(filters.maxResults && {
          maxResults: filters.maxResults.toString(),
        }),
      });

      const response = await fetch(
        `/api/youtube/search?${queryParams.toString()}`
      );

      if (!response.ok) {
        const errorData = await response.json();
        if (response.status === 429) {
          // Rate limit exceeded - redirect to pricing page with message
          // const remaining = errorData.remaining || 0;
          // router.push(
          //   `/pricing?message=${encodeURIComponent(`You have ${remaining} searches remaining. Upgrade plan for more searches.`)}`
          // );
          router.push(
            `/pricing?message=${encodeURIComponent(`You have exhausted your searches for today. Kindly upgrade your plan to search.`)}`
          );
          return;
        }
        throw new Error(errorData.error || "Failed to search videos");
      }

      const data = await response.json();
      setVideos(data.videos);
      setHasSearched(true);

      // Notify parent component to refresh rate limit info
      if (onSearchComplete) {
        onSearchComplete();
      }
    } catch (err: any) {
      setError(err.message || "An error occurred while searching");
      setVideos([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTopicSelect = (topic: string) => {
    setQuery(topic);
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      <form onSubmit={handleSearch} className="mb-8">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
          <Input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Enter a topic (e.g., productivity, hair care, python tutorials)"
            className="pl-10 py-6 text-lg"
          />
          <Button
            type="submit"
            className="absolute right-1 top-1/2 transform -translate-y-1/2"
            disabled={isLoading}
          >
            {isLoading ? "Searching..." : "Search"}
          </Button>
        </div>

        <div className="mt-4">
          <SearchFiltersComponent filters={filters} setFilters={setFilters} />
        </div>

        <TrendingSearches onTopicSelect={handleTopicSelect} />
      </form>

      {error && (
        <div className="bg-destructive/10 text-destructive p-4 rounded-md mb-6">
          {error}
        </div>
      )}

      {hasSearched && (
        <VideoResults
          videos={videos}
          isLoading={isLoading}
          searchTerm={query}
        />
      )}
    </div>
  );
}
