"use client";

import { useState } from "react";
import { SearchFilters } from "@/utils/youtube-api";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";
import { ChevronDown, ChevronUp, SlidersHorizontal } from "lucide-react";

interface SearchFiltersProps {
  filters: SearchFilters;
  setFilters: (filters: SearchFilters) => void;
}

// Language options
const languages = [
  { code: "en", name: "English" },
  { code: "es", name: "Spanish" },
  { code: "fr", name: "French" },
  { code: "de", name: "German" },
  { code: "pt", name: "Portuguese" },
  { code: "ru", name: "Russian" },
  { code: "ja", name: "Japanese" },
  { code: "ko", name: "Korean" },
  { code: "zh", name: "Chinese" },
  { code: "ar", name: "Arabic" },
  { code: "hi", name: "Hindi" },
];

// Region options (countries)
const regions = [
  { code: "US", name: "United States" },
  { code: "GB", name: "United Kingdom" },
  { code: "CA", name: "Canada" },
  { code: "AU", name: "Australia" },
  { code: "IN", name: "India" },
  { code: "DE", name: "Germany" },
  { code: "FR", name: "France" },
  { code: "JP", name: "Japan" },
  { code: "BR", name: "Brazil" },
  { code: "MX", name: "Mexico" },
  { code: "KR", name: "South Korea" },
  { code: "RU", name: "Russia" },
  { code: "ES", name: "Spain" },
  { code: "IT", name: "Italy" },
  { code: "NG", name: "Nigeria" },
  { code: "ZA", name: "South Africa" },
];

// Time range options
const timeRanges = [
  { value: "day", label: "Last 24 hours" },
  { value: "week", label: "Last week" },
  { value: "month", label: "Last month" },
  { value: "year", label: "Last year" },
  { value: "all", label: "All time" },
];

export default function SearchFiltersComponent({
  filters,
  setFilters,
}: SearchFiltersProps) {
  const [isOpen, setIsOpen] = useState(false);

  const handleRegionChange = (value: string) => {
    setFilters({ ...filters, region: value });
  };

  const handleLanguageChange = (value: string) => {
    setFilters({ ...filters, language: value });
  };

  const handleTimeRangeChange = (value: string) => {
    setFilters({ ...filters, timeRange: value as SearchFilters["timeRange"] });
  };

  const handleMaxResultsChange = (value: string) => {
    setFilters({ ...filters, maxResults: parseInt(value, 10) });
  };

  return (
    <Collapsible
      open={isOpen}
      onOpenChange={setIsOpen}
      className="w-full space-y-2"
    >
      <div className="flex items-center justify-between">
        <CollapsibleTrigger asChild>
          <Button variant="outline" size="sm" className="flex items-center gap-2">
            <SlidersHorizontal className="h-4 w-4" />
            Filters
            {isOpen ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </CollapsibleTrigger>
      </div>
      <CollapsibleContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 pt-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Region</label>
            <Select
              value={filters.region}
              onValueChange={handleRegionChange}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select region" />
              </SelectTrigger>
              <SelectContent>
                {regions.map((region) => (
                  <SelectItem key={region.code} value={region.code}>
                    {region.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Language</label>
            <Select
              value={filters.language}
              onValueChange={handleLanguageChange}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select language" />
              </SelectTrigger>
              <SelectContent>
                {languages.map((language) => (
                  <SelectItem key={language.code} value={language.code}>
                    {language.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Time Range</label>
            <Select
              value={filters.timeRange}
              onValueChange={handleTimeRangeChange}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select time range" />
              </SelectTrigger>
              <SelectContent>
                {timeRanges.map((range) => (
                  <SelectItem key={range.value} value={range.value}>
                    {range.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Results</label>
            <Select
              value={filters.maxResults?.toString()}
              onValueChange={handleMaxResultsChange}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Number of results" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5 videos</SelectItem>
                <SelectItem value="10">10 videos</SelectItem>
                <SelectItem value="15">15 videos</SelectItem>
                <SelectItem value="20">20 videos</SelectItem>
                <SelectItem value="25">25 videos</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CollapsibleContent>
    </Collapsible>
  );
}
