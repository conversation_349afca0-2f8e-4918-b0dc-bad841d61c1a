"use client";

import { useState, FormEvent } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import TrendingSearches from "@/components/trending-searches";
import { useRouter } from "next/navigation";
import { createClient } from "@/utils/supabase/client";

export default function LandingSearch() {
  const [query, setQuery] = useState("");
  const router = useRouter();

  const handleSearch = async (e: FormEvent) => {
    e.preventDefault();

    if (!query.trim()) {
      return;
    }

    // Check if user is authenticated
    const supabase = createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (user) {
      // User is authenticated, redirect to search page with query
      router.push(`/search?q=${encodeURIComponent(query.trim())}`);
    } else {
      // User is not authenticated, store query and redirect to sign up
      sessionStorage.setItem('pendingSearchQuery', query.trim());
      router.push('/sign-up');
    }
  };

  const handleTopicSelect = (topic: string) => {
    setQuery(topic);
  };

  return (
    <div className="max-w-2xl mx-auto mb-8">
      <form onSubmit={handleSearch}>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
          <Input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Enter a topic (e.g., productivity, hair care, python tutorials)"
            className="pl-10 py-6 text-lg"
          />
          <Button 
            type="submit"
            className="absolute right-1 top-1/2 transform -translate-y-1/2"
          >
            Search
          </Button>
        </div>
      </form>
      <TrendingSearches onTopicSelect={handleTopicSelect} />
    </div>
  );
}
