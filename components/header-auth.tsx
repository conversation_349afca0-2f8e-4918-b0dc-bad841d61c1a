"use client";

import { useEffect, useState } from "react";
import { signOutAction } from "@/app/actions";
import Link from "next/link";
import { Button } from "./ui/button";
import { Badge } from "./ui/badge";
import { createClient } from "@/utils/supabase/client";
import { Crown, Zap, Rocket, Coins } from "lucide-react";

interface RateLimitInfo {
  tier: "free" | "pro" | "business";
  limit: number;
  used: number;
  remaining: number;
}

interface User {
  email?: string;
  user_metadata?: {
    firstName?: string;
    lastName?: string;
    full_name?: string;
    name?: string;
  };
}

const tierConfig = {
  free: {
    name: "Basic",
    icon: Zap,
    badgeVariant: "secondary" as const,
  },
  pro: {
    name: "Pro",
    icon: Crown,
    badgeVariant: "default" as const,
  },
  business: {
    name: "Business",
    icon: Rocket,
    badgeVariant: "default" as const,
  },
};

export default function AuthButton() {
  const [user, setUser] = useState<User | null>(null);
  const [rateLimitInfo, setRateLimitInfo] = useState<RateLimitInfo | null>(
    null
  );
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchUserAndRateLimit();

    // Listen for custom search completion events
    const handleSearchComplete = () => {
      if (user) {
        fetchUserAndRateLimit();
      }
    };

    window.addEventListener("searchCompleted", handleSearchComplete);
    return () =>
      window.removeEventListener("searchCompleted", handleSearchComplete);
  }, [user]);

  const fetchUserAndRateLimit = async () => {
    try {
      const supabase = createClient();
      const {
        data: { user: authUser },
      } = await supabase.auth.getUser();

      if (authUser) {
        setUser(authUser);

        // Fetch rate limit info
        const response = await fetch("/api/rate-limit/info");
        if (response.ok) {
          const data = await response.json();
          setRateLimitInfo(data);
        }
      }
    } catch (error) {
      console.error("Error fetching user and rate limit info:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center gap-4">
        <div className="animate-pulse">
          <div className="h-4 bg-muted rounded w-32"></div>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex gap-2">
        <Button asChild size="sm" variant={"outline"}>
          <Link href="/sign-in">Sign in</Link>
        </Button>
        <Button asChild size="sm" variant={"default"}>
          <Link href="/sign-up">Sign up</Link>
        </Button>
      </div>
    );
  }

  // Try multiple possible locations for the user's name
  const displayName = user.user_metadata?.firstName
    ? `${user.user_metadata.firstName} ${user.user_metadata.lastName || ""}`.trim()
    : user.user_metadata?.full_name
      ? user.user_metadata.full_name
      : user.user_metadata?.name
        ? user.user_metadata.name
        : user.email;

  const tier = rateLimitInfo?.tier || "free";
  const remaining = rateLimitInfo?.remaining || 0;
  const config = tierConfig[tier];
  const Icon = config.icon;

  return (
    <div className="flex items-center gap-4 text-sm">
      {/* Greeting and Name */}
      <Link href="/dashboard" className="hover:text-primary transition-colors">
        <span className="text-muted-foreground">Hi,</span>{" "}
        <span className="font-medium">{displayName}</span>
      </Link>

      {/* Tier Badge */}
      <Badge variant={config.badgeVariant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.name}
      </Badge>

      {/* Remaining Searches (only for Basic tier) */}
      {tier === "free" && (
        <div className="flex items-center gap-1 text-muted-foreground">
          <Coins className="h-4 w-4 text-yellow-500" />
          <span className="font-medium">{remaining}</span>
        </div>
      )}

      {/* Sign Out Button */}
      <form action={signOutAction}>
        <Button type="submit" variant="outline" size="sm">
          Sign out
        </Button>
      </form>
    </div>
  );
}
