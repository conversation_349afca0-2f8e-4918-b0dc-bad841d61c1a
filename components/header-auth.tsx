import { signOutAction } from "@/app/actions";
import <PERSON> from "next/link";
import { But<PERSON> } from "./ui/button";
import { createClient } from "@/utils/supabase/server";

export default async function AuthButton() {
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  // Try multiple possible locations for the user's name
  const displayName = user?.user_metadata?.firstName
    ? `${user.user_metadata.firstName} ${user.user_metadata.lastName || ""}`.trim()
    : user?.user_metadata?.full_name
      ? user.user_metadata.full_name
      : user?.user_metadata?.name
        ? user.user_metadata.name
        : user?.email;

  return user ? (
    <div className="flex items-center gap-4">
      <Link href="/dashboard" className="hover:text-primary transition-colors">
        Hi, {displayName}
      </Link>
      <form action={signOutAction}>
        <Button type="submit" variant={"outline"}>
          Sign out
        </Button>
      </form>
    </div>
  ) : (
    <div className="flex gap-2">
      <Button asChild size="sm" variant={"outline"}>
        <Link href="/sign-in">Sign in</Link>
      </Button>
      <Button asChild size="sm" variant={"default"}>
        <Link href="/sign-up">Sign up</Link>
      </Button>
    </div>
  );
}
