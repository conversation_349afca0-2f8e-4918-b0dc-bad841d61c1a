"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Settings } from "lucide-react";

export default function ManageSubscriptionButton() {
  const handleManageSubscription = async () => {
    try {
      const response = await fetch('/api/stripe/portal', {
        method: 'POST',
      });
      
      if (response.ok) {
        const { url } = await response.json();
        window.location.href = url;
      } else {
        alert('No active subscription found.');
      }
    } catch (error) {
      console.error('Error opening customer portal:', error);
      alert('Failed to open subscription management. Please try again.');
    }
  };

  return (
    <Button 
      variant="outline" 
      className="w-full justify-start"
      onClick={handleManageSubscription}
    >
      <Settings className="h-4 w-4 mr-2" />
      Manage Subscription
    </Button>
  );
}
