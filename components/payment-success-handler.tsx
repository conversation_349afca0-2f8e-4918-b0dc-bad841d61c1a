"use client";

import { useEffect, useState } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { Card, CardContent } from "@/components/ui/card";
import { CheckCircle, X } from "lucide-react";
import { Button } from "@/components/ui/button";

export default function PaymentSuccessHandler() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [isProcessing, setIsProcessing] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const success = searchParams.get('success');
    const sessionId = searchParams.get('session_id');

    if (success === 'true' && sessionId) {
      handlePaymentSuccess(sessionId);
    }
  }, [searchParams]);

  const handlePaymentSuccess = async (sessionId: string) => {
    setIsProcessing(true);
    try {
      const response = await fetch(`/api/stripe/success?session_id=${sessionId}`);
      const data = await response.json();

      if (response.ok) {
        setSuccessMessage(data.message || 'Payment successful! Your plan has been upgraded.');
        // Clean up URL parameters
        router.replace('/dashboard');
      } else {
        setError(data.error || 'Failed to process payment');
      }
    } catch (error) {
      console.error('Error processing payment success:', error);
      setError('Failed to process payment. Please contact support if your plan was not upgraded.');
    } finally {
      setIsProcessing(false);
    }
  };

  const dismissMessage = () => {
    setSuccessMessage(null);
    setError(null);
  };

  if (!successMessage && !error && !isProcessing) {
    return null;
  }

  return (
    <div className="mb-6">
      {isProcessing && (
        <Card className="border-blue-200 bg-blue-50 dark:bg-blue-950/20">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
              <span className="text-blue-800 dark:text-blue-200">
                Processing your payment...
              </span>
            </div>
          </CardContent>
        </Card>
      )}

      {successMessage && (
        <Card className="border-green-200 bg-green-50 dark:bg-green-950/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="text-green-800 dark:text-green-200 font-medium">
                  {successMessage}
                </span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={dismissMessage}
                className="text-green-600 hover:text-green-700"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {error && (
        <Card className="border-red-200 bg-red-50 dark:bg-red-950/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <X className="h-5 w-5 text-red-600" />
                <span className="text-red-800 dark:text-red-200 font-medium">
                  {error}
                </span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={dismissMessage}
                className="text-red-600 hover:text-red-700"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
