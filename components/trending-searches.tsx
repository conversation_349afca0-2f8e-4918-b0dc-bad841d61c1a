"use client";

import { But<PERSON> } from "@/components/ui/button";

const trendingTopics = [
  "productivity hacks",
  "morning routine",
  "coding tutorials",
  "fitness tips",
  "cooking recipes",
  "travel vlogs",
  "tech reviews",
  "digital marketing",
];

interface TrendingSearchesProps {
  onTopicSelect?: (topic: string) => void;
}

export default function TrendingSearches({
  onTopicSelect,
}: TrendingSearchesProps) {
  const handleTopicClick = (topic: string) => {
    if (onTopicSelect) {
      onTopicSelect(topic);
    }
  };

  return (
    <div className="mt-3">
      <p className="text-sm text-muted-foreground mb-2">Trending searches:</p>
      <div className="flex flex-wrap gap-2">
        {trendingTopics.map((topic, index) => (
          <Button
            key={index}
            variant="outline"
            size="sm"
            onClick={() => handleTopicClick(topic)}
            className="rounded-full text-xs"
          >
            {topic}
          </Button>
        ))}
      </div>
    </div>
  );
}
